<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Transaction History - Chicken Road Game</title>
  <style>
    :root {
      --main-color: #00ECBE;
      --main-gradient-start: #7AFEC3;
      --main-gradient-end: #02AFB6;
      --main_gradient-color: linear-gradient(180deg, var(--main-gradient-start), var(--main-gradient-end));
      --main_gradient-color2: linear-gradient(90deg, var(--main-gradient-start), var(--main-gradient-end));
      --bg_HomeModule_Stroke: #224BA2;
      --bg_HomeModule_Padding: linear-gradient(180deg, #001C54 0%, #000C33 100%);
      --norm_red-color: #D23838;
      --norm_green-color: #17B15E;
      --norm_secondary-color: #DD9138;
      --norm_Purple-color: #9B48DB;
      --norm_bule-color: #5088D3;
      --button_dis_color: #3D4863;
      --Secondary_red_color: #FD565C;
      --Secondary_green_color: #40AD72;
      --Secondary_Color1: #FFEBEC;
      --Secondary_Color2: #DAFFEB;
      --Secondary_moto_Color9: #239DA1;
      --Secondary_moto_Color8: #163B86;
      --text_color_L1: #E3EFFF;
      --text_color_L2: #92A8E3;
      --text_color_L3: #6F80A4;
      --text_color_L4: #05012B;
      --bg_color_L1: #05012B;
      --bg_color_L2: #011341;
      --bg_color_L3: #001C54;
      --Dividing-line_color: #022C68;
      --sheet_nva_color: #2C5ECA;
      --sheet_detail_bg_color: #000C33;
      --icon1: rgba(12, 195, 159, .6);
      --icon2: rgba(12, 195, 159, .25);
      --tab1: #21D9CC;
      --tab2: #BED921;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    body {
      background: var(--bg_HomeModule_Padding);
      min-height: 100vh;
      color: var(--text_color_L1);
      padding: 0;
      padding-bottom: 70px;
      position: relative;
      overflow-x: hidden;
    }
    
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 20% 80%, rgba(0, 236, 190, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(155, 72, 219, 0.1) 0%, transparent 50%);
      pointer-events: none;
      z-index: -1;
      animation: backgroundShift 20s ease-in-out infinite;
    }
    
    @keyframes backgroundShift {
      0%, 100% { opacity: 0.5; }
      50% { opacity: 1; }
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 12px 15px;
      background: var(--bg_color_L2);
      box-shadow: 0 8px 25px rgba(0, 28, 84, 0.4);
      border-bottom: 2px solid var(--Dividing-line_color);
    }
    
    .logo-section {
      display: flex;
      align-items: center;
    }
    
    .user-header-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
    }
    
    .user-header-name {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--main-color);
    }
    
    .user-header-balance {
      font-size: 0.8rem;
      color: #fbbf24;
      font-weight: 500;
    }
    
    .chicken-icon {
      width: 35px;
      height: 35px;
      background: var(--main_gradient-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      margin-right: 12px;
      box-shadow: 0 4px 15px rgba(0, 236, 190, 0.4);
    }
    
    .app-title {
      font-size: 1.2rem;
      font-weight: bold;
      color: var(--text_color_L1);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .nav-buttons {
      display: flex;
      gap: 12px;
    }
    
    .nav-btn {
      padding: 10px 18px;
      border-radius: 8px;
      font-size: 0.85rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
    }
    
    .nav-btn.back {
      background: rgba(255, 255, 255, 0.15);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
    }
    
    .nav-btn.back:hover {
      background: rgba(255, 255, 255, 0.25);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
    }
    
    .main-content {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    .transaction-card {
      background: var(--bg_color_L2);
      border-radius: 16px;
      border: 2px solid var(--Dividing-line_color);
      box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
      padding: 20px;
      margin-bottom: 20px;
      backdrop-filter: blur(10px);
    }
    
    .card-title {
      color: var(--text_color_L1);
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .filter-section {
      margin-bottom: 20px;
    }
    
    .filter-dropdown {
      position: relative;
      display: inline-block;
    }
    
    .filter-btn {
      background: var(--main_gradient-color);
      color: var(--text_color_L4);
      border: none;
      border-radius: 8px;
      padding: 10px 16px;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 4px 15px rgba(0, 236, 190, 0.3);
    }
    
    .filter-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 236, 190, 0.4);
    }
    
    .filter-options {
      position: absolute;
      top: 100%;
      left: 0;
      margin-top: 8px;
      background: var(--bg_color_L3);
      border: 2px solid var(--Dividing-line_color);
      border-radius: 12px;
      overflow: hidden;
      z-index: 1000;
      min-width: 200px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      display: none;
    }
    
    .filter-option {
      padding: 12px 16px;
      color: var(--text_color_L1);
      cursor: pointer;
      transition: all 0.3s ease;
      border-bottom: 1px solid var(--Dividing-line_color);
    }
    
    .filter-option:last-child {
      border-bottom: none;
    }
    
    .filter-option:hover {
      background: var(--bg_color_L2);
      color: var(--main-color);
    }
    
    .loading-state {
      text-align: center;
      padding: 40px 20px;
      color: var(--text_color_L2);
      font-size: 1rem;
    }
    
    .error-state {
      text-align: center;
      padding: 40px 20px;
      color: var(--Secondary_red_color);
      font-size: 1rem;
    }
    
    .transaction-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    
    .transaction-table th {
      background: var(--bg_color_L3);
      color: var(--text_color_L1);
      font-weight: 600;
      padding: 12px 16px;
      text-align: left;
      border-bottom: 2px solid var(--Dividing-line_color);
      font-size: 0.9rem;
    }
    
    .transaction-table td {
      padding: 12px 16px;
      border-bottom: 1px solid var(--Dividing-line_color);
      color: var(--text_color_L2);
      font-size: 0.85rem;
    }
    
    .transaction-table tr:hover {
      background: var(--bg_color_L3);
    }
    
    .type-badge {
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
    }
    
    .type-deposit {
      background: rgba(23, 177, 94, 0.2);
      color: var(--Secondary_green_color);
      border: 1px solid rgba(23, 177, 94, 0.3);
    }
    
    .type-withdrawal {
      background: rgba(221, 145, 56, 0.2);
      color: var(--norm_secondary-color);
      border: 1px solid rgba(221, 145, 56, 0.3);
    }
    
    .type-bet {
      background: rgba(80, 136, 211, 0.2);
      color: var(--norm_bule-color);
      border: 1px solid rgba(80, 136, 211, 0.3);
    }
    
    .type-win {
      background: rgba(23, 177, 94, 0.2);
      color: var(--Secondary_green_color);
      border: 1px solid rgba(23, 177, 94, 0.3);
    }
    
    .type-loss {
      background: rgba(210, 56, 56, 0.2);
      color: var(--Secondary_red_color);
      border: 1px solid rgba(210, 56, 56, 0.3);
    }
    
    .type-bonus {
      background: rgba(155, 72, 219, 0.2);
      color: var(--norm_Purple-color);
      border: 1px solid rgba(155, 72, 219, 0.3);
    }
    
    .type-referral {
      background: rgba(80, 136, 211, 0.2);
      color: var(--norm_bule-color);
      border: 1px solid rgba(80, 136, 211, 0.3);
    }
    
    .status-badge {
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
    }
    
    .status-completed {
      background: rgba(23, 177, 94, 0.2);
      color: var(--Secondary_green_color);
      border: 1px solid rgba(23, 177, 94, 0.3);
    }
    
    .status-pending {
      background: rgba(221, 145, 56, 0.2);
      color: var(--norm_secondary-color);
      border: 1px solid rgba(221, 145, 56, 0.3);
    }
    
    .status-failed {
      background: rgba(210, 56, 56, 0.2);
      color: var(--Secondary_red_color);
      border: 1px solid rgba(210, 56, 56, 0.3);
    }
    
    .status-cancelled {
      background: rgba(111, 128, 164, 0.2);
      color: var(--text_color_L3);
      border: 1px solid rgba(111, 128, 164, 0.3);
    }
    
    .amount-positive {
      color: var(--Secondary_green_color);
      font-weight: 600;
    }
    
    .amount-negative {
      color: var(--Secondary_red_color);
      font-weight: 600;
    }
    
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 12px;
      margin-top: 20px;
    }
    
    .pagination-btn {
      background: var(--bg_color_L3);
      color: var(--text_color_L1);
      border: 2px solid var(--Dividing-line_color);
      border-radius: 8px;
      padding: 8px 16px;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .pagination-btn:hover:not(:disabled) {
      background: var(--bg_color_L2);
      border-color: var(--main-color);
      transform: translateY(-1px);
    }
    
    .pagination-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .page-info {
      color: var(--text_color_L2);
      font-size: 0.9rem;
      font-weight: 500;
    }
    
    /* Modal Styles */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.7);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    
    .modal-content {
      background: var(--bg_color_L2);
      border-radius: 18px;
      padding: 24px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border: 2px solid var(--Dividing-line_color);
      text-align: center;
      max-width: 320px;
      width: 90%;
      position: relative;
    }
    
    .modal-close {
      position: absolute;
      top: 12px;
      right: 12px;
      background: rgba(64,75,79,0.12);
      border: none;
      font-size: 2.1rem;
      color: #a1a1aa;
      cursor: pointer;
      border-radius: 50%;
      width: 38px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.2s, color 0.2s;
    }
    
    .modal-close:hover {
      color: #f87171;
      background: rgba(64,75,79,0.22);
    }
    
    .modal-title {
      font-size: 1.5rem;
      font-weight: bold;
      color: var(--text_color_L1);
      margin-bottom: 16px;
    }
    
    /* Auth Modal */
    .auth-form {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 20px;
    }
    
    .auth-toggle {
      color: var(--main-color);
      cursor: pointer;
      text-decoration: underline;
      font-size: 0.9rem;
    }
    
    .auth-toggle:hover {
      color: var(--main-gradient-start);
    }
    
    .error-message {
      color: var(--Secondary_red_color);
      font-size: 0.9rem;
      margin-top: 8px;
    }
    
    /* Bottom Navigation Bar */
    .bottom-nav {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: var(--bg_color_L2);
      backdrop-filter: blur(15px);
      border-top: 2px solid var(--Dividing-line_color);
      padding: 10px 0;
      display: flex;
      justify-content: space-around;
      align-items: center;
      z-index: 1000;
    }
    
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      color: var(--text_color_L2);
      font-size: 0.8rem;
      font-weight: 500;
      transition: all 0.3s ease;
      padding: 8px 12px;
      border-radius: 10px;
      min-width: 60px;
    }
    
    .nav-item:hover {
      color: var(--text_color_L1);
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 236, 190, 0.3);
    }
    
    .nav-item:active {
      transform: translateY(0px) scale(0.95);
    }
    
    .nav-item.active {
      color: var(--main-color);
      background: rgba(0, 236, 190, 0.2);
    }
    
    .nav-icon {
      font-size: 1.2rem;
      margin-bottom: 4px;
    }
    
    .nav-text {
      font-size: 0.7rem;
      text-align: center;
    }
    
    @media (max-width: 768px) {
      .main-content {
        padding: 0 15px;
      }
      
      .transaction-card {
        padding: 15px;
      }
      
      .transaction-table {
        font-size: 0.8rem;
      }
      
      .transaction-table th,
      .transaction-table td {
        padding: 8px 12px;
      }
      
      .filter-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
      }
    }
    
    @media (max-width: 480px) {
      body {
        padding: 0;
        padding-bottom: 80px;
      }
      
      .header {
        margin-bottom: 20px;
        padding: 15px 18px;
      }
      
      .app-title {
        font-size: 1.1rem;
      }
      
      .nav-btn {
        padding: 8px 15px;
        font-size: 0.8rem;
      }
      
      .transaction-table {
        font-size: 0.75rem;
      }
      
      .transaction-table th,
      .transaction-table td {
        padding: 6px 8px;
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <div class="header">
    <div class="logo-section">
      <div class="chicken-icon">🐔</div>
      <div class="app-title">TRANSACTION HISTORY</div>
    </div>
    <div class="user-header-info" id="userHeaderInfo" style="display: none;">
      <div class="user-header-name" id="userHeaderName">Welcome!</div>
      <div class="user-header-balance" id="userHeaderBalance">Balance: ₹0</div>
    </div>
    <div class="nav-buttons">
      <button class="nav-btn back" onclick="window.location.href='index.html'">Back</button>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <div class="transaction-card">
      <div class="card-title">Transaction History</div>
      
      <!-- Filter Section -->
      <div class="filter-section">
        <div class="filter-dropdown">
          <button id="filterDropdown" class="filter-btn">
            <span id="filterText">All Transactions</span>
            <span>▼</span>
          </button>
          <div id="filterOptions" class="filter-options">
            <div class="filter-option" data-type="">All Transactions</div>
            <div class="filter-option" data-type="deposit">Deposits</div>
            <div class="filter-option" data-type="withdrawal">Withdrawals</div>
          </div>
        </div>
      </div>
      
      <!-- Loading State -->
      <div id="loadingState" class="loading-state" style="display: none;">
        Loading transactions...
      </div>
      
      <!-- Error State -->
      <div id="errorState" class="error-state" style="display: none;">
        <div id="errorMessage"></div>
      </div>
      
      <!-- Transaction Table -->
      <div class="table-container">
        <table class="transaction-table">
          <thead>
            <tr>
              <th>Type</th>
              <th>Amount</th>
              <th>Status</th>
              <th>Description</th>
              <th>Date/Time</th>
            </tr>
          </thead>
          <tbody id="transactionsTable">
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      <div id="pagination" class="pagination" style="display: none;">
        <button id="prevBtn" class="pagination-btn">Previous</button>
        <span id="pageInfo" class="page-info"></span>
        <button id="nextBtn" class="pagination-btn">Next</button>
      </div>
    </div>
  </div>

  <!-- Auth Modal -->
  <div id="authModal" class="modal">
    <div class="modal-content">
      <button class="modal-close" onclick="closeAuthModal()">&times;</button>
      <div class="modal-title" id="authTitle">Login</div>
      <div class="auth-form">
        <input type="text" id="authUsername" class="form-input" placeholder="Username">
        <input type="password" id="authPassword" class="form-input" placeholder="Password">
        <div class="error-message" id="authError" style="display: none;"></div>
      </div>
      <button class="modal-btn" id="authActionBtn">Login</button>
      <div style="margin-top: 12px;">
        <span class="auth-toggle" id="toggleAuthMode">Don't have an account? Register</span>
      </div>
    </div>
  </div>

  <script>
    let currentPage = 1;
    let currentFilter = '';
    let totalPages = 1;
    
    // Auth integration
    function showAuthModal() {
      document.getElementById('authModal').style.display = 'flex';
      document.getElementById('authError').style.display = 'none';
    }
    
    function closeAuthModal() {
      document.getElementById('authModal').style.display = 'none';
    }
    
    function fetchUser() {
      return fetch('api/user.php').then(r => r.json());
    }
    
    function requireLogin() {
      fetchUser().then(data => {
        if (!data.success) showAuthModal();
        else loadUserData();
      });
    }
    
    window.addEventListener('DOMContentLoaded', requireLogin);
    
    // Auth modal logic
    let authMode = 'login';
    document.getElementById('toggleAuthMode').onclick = function() {
      authMode = authMode === 'login' ? 'register' : 'login';
      document.getElementById('authTitle').textContent = authMode === 'login' ? 'Login' : 'Register';
      document.getElementById('authActionBtn').textContent = authMode === 'login' ? 'Login' : 'Register';
      document.getElementById('toggleAuthMode').textContent = authMode === 'login' ? 'Don\'t have an account? Register' : 'Already have an account? Login';
    };
    
    document.getElementById('authActionBtn').onclick = function() {
      const username = document.getElementById('authUsername').value.trim();
      const password = document.getElementById('authPassword').value;
      const errorDiv = document.getElementById('authError');
      errorDiv.style.display = 'none';
      
      let url = authMode === 'login' ? 'api/login.php' : 'api/register.php';
      let body = authMode === 'login' ? { username, password } : { username, email: username, password };
      
      fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })
      .then(r => r.json())
      .then(data => {
        if (data.success) {
          closeAuthModal();
          loadUserData();
        } else {
          errorDiv.textContent = data.error || 'Authentication failed.';
          errorDiv.style.display = 'block';
        }
      })
      .catch(() => {
        errorDiv.textContent = 'Network error.';
        errorDiv.style.display = 'block';
      });
    };

    // Load user data
    function loadUserData() {
      // Load user balance
      fetch('api/user.php')
        .then(r => r.json())
        .then(data => {
          if (data.success) {
            // Update header info
            document.getElementById('userHeaderInfo').style.display = 'flex';
            document.getElementById('userHeaderName').textContent = `Welcome, ${data.user.username}!`;
            document.getElementById('userHeaderBalance').textContent = `Balance: ₹${parseFloat(data.user.balance || 0).toFixed(2)}`;
          }
        });
      
      // Load transactions
      loadTransactions();
    }

    // Load transactions
    function loadTransactions() {
      const loadingState = document.getElementById('loadingState');
      const errorState = document.getElementById('errorState');
      const table = document.getElementById('transactionsTable');
      
      loadingState.style.display = 'block';
      errorState.style.display = 'none';
      table.innerHTML = '';
      
      let url = `api/transactions.php?page=${currentPage}&limit=10`;
      if (currentFilter) url += `&type=${currentFilter}`;
      
      fetch(url)
        .then(r => r.json())
        .then(data => {
          loadingState.style.display = 'none';
          if (data.success) {
            displayTransactions(data.transactions);
            updatePagination(data.pagination);
          } else {
            showError(data.error || 'Failed to load transactions');
          }
        })
        .catch(() => {
          loadingState.style.display = 'none';
          showError('Network error');
        });
    }
    
    function displayTransactions(transactions) {
      const table = document.getElementById('transactionsTable');
      table.innerHTML = '';
      
      if (transactions.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="5" style="text-align: center; padding: 40px 20px; color: var(--text_color_L3);">No transactions found</td>';
        table.appendChild(tr);
        return;
      }
      
      transactions.forEach(tx => {
        const tr = document.createElement('tr');
        const typeClass = getTypeClass(tx.type);
        const statusClass = getStatusClass(tx.status);
        const amountClass = parseFloat(tx.amount) >= 0 ? 'amount-positive' : 'amount-negative';
        const amountSign = parseFloat(tx.amount) >= 0 ? '+' : '';
        
        tr.innerHTML = `
          <td><span class="type-badge ${typeClass}">${tx.type.toUpperCase()}</span></td>
          <td class="${amountClass}">${amountSign}₹${parseFloat(tx.amount).toFixed(2)}</td>
          <td><span class="status-badge ${statusClass}">${tx.status.toUpperCase()}</span></td>
          <td>${tx.description || '-'}</td>
          <td>${new Date(tx.created_at).toLocaleString()}</td>
        `;
        table.appendChild(tr);
      });
    }
    
    function getTypeClass(type) {
      switch(type) {
        case 'deposit': return 'type-deposit';
        case 'withdrawal': return 'type-withdrawal';
        case 'bet': return 'type-bet';
        case 'win': return 'type-win';
        case 'loss': return 'type-loss';
        case 'bonus': return 'type-bonus';
        case 'referral': return 'type-referral';
        default: return 'type-deposit';
      }
    }
    
    function getStatusClass(status) {
      switch(status) {
        case 'completed': return 'status-completed';
        case 'pending': return 'status-pending';
        case 'failed': return 'status-failed';
        case 'cancelled': return 'status-cancelled';
        default: return 'status-pending';
      }
    }
    
    function updatePagination(pagination) {
      const paginationDiv = document.getElementById('pagination');
      const pageInfo = document.getElementById('pageInfo');
      const prevBtn = document.getElementById('prevBtn');
      const nextBtn = document.getElementById('nextBtn');
      
      totalPages = pagination.total_pages;
      currentPage = pagination.current_page;
      
      pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
      prevBtn.disabled = !pagination.has_prev;
      nextBtn.disabled = !pagination.has_next;
      
      paginationDiv.style.display = 'flex';
    }
    
    function showError(message) {
      const errorState = document.getElementById('errorState');
      const errorMessage = document.getElementById('errorMessage');
      errorMessage.textContent = message;
      errorState.style.display = 'block';
    }
    
    // Filter dropdown
    document.getElementById('filterDropdown').addEventListener('click', function(e) {
      e.stopPropagation();
      const options = document.getElementById('filterOptions');
      options.style.display = options.style.display === 'block' ? 'none' : 'block';
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('filterDropdown');
      const options = document.getElementById('filterOptions');
      if (!dropdown.contains(event.target) && !options.contains(event.target)) {
        options.style.display = 'none';
      }
    });
    
    // Filter option selection
    document.querySelectorAll('.filter-option').forEach(option => {
      option.addEventListener('click', function() {
        const selectedType = this.dataset.type;
        const filterText = selectedType === '' ? 'All Transactions' : selectedType.charAt(0).toUpperCase() + selectedType.slice(1);
        document.getElementById('filterText').textContent = filterText;
        currentFilter = selectedType;
        currentPage = 1;
        loadTransactions();
        document.getElementById('filterOptions').style.display = 'none';
      });
    });
    
    // Pagination buttons
    document.getElementById('prevBtn').addEventListener('click', function() {
      if (currentPage > 1) {
        currentPage--;
        loadTransactions();
      }
    });
    
    document.getElementById('nextBtn').addEventListener('click', function() {
      if (currentPage < totalPages) {
        currentPage++;
        loadTransactions();
      }
    });
    
    // Function to check authentication before navigation
    async function checkAuthAndNavigate(targetPage) {
      try {
        const response = await fetch('api/user.php');
        const data = await response.json();
        
        if (data.success) {
          // User is logged in, navigate to target page
          window.location.href = targetPage;
        } else {
          // User is not logged in, show message and stay on current page
          alert('Please login first to access this page');
        }
      } catch (error) {
        // Network error, show message
        alert('Please login first to access this page');
      }
    }
  </script>
  
  <!-- Bottom Navigation Bar -->
  <div class="bottom-nav">
    <a href="index.html" class="nav-item">
      <div class="nav-icon">🏠</div>
      <div class="nav-text">Home</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('refer.html')" class="nav-item">
      <div class="nav-icon">👥</div>
      <div class="nav-text">Promotion</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('game.html')" class="nav-item">
      <div class="nav-icon">🎮</div>
      <div class="nav-text">Games</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('bet-history.html')" class="nav-item">
      <div class="nav-icon">💰</div>
      <div class="nav-text">My Bet</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('tranjection.html')" class="nav-item active">
      <div class="nav-icon">👤</div>
      <div class="nav-text">My</div>
    </a>
  </div>
</body>
</html> 