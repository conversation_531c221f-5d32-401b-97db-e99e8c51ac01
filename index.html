<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Login/Register - Chicken Road Game</title>
  <style>
    :root {
      --main-color: #00ECBE;
      --main-gradient-start: #7AFEC3;
      --main-gradient-end: #02AFB6;
      --main_gradient-color: linear-gradient(180deg, var(--main-gradient-start), var(--main-gradient-end));
      --main_gradient-color2: linear-gradient(90deg, var(--main-gradient-start), var(--main-gradient-end));
      --bg_HomeModule_Stroke: #224BA2;
      --bg_HomeModule_Padding: linear-gradient(180deg, #001C54 0%, #000C33 100%);
      --norm_red-color: #D23838;
      --norm_green-color: #17B15E;
      --norm_secondary-color: #DD9138;
      --norm_Purple-color: #9B48DB;
      --norm_bule-color: #5088D3;
      --button_dis_color: #3D4863;
      --Secondary_red_color: #FD565C;
      --Secondary_green_color: #40AD72;
      --Secondary_Color1: #FFEBEC;
      --Secondary_Color2: #DAFFEB;
      --Secondary_moto_Color9: #239DA1;
      --Secondary_moto_Color8: #163B86;
      --text_color_L1: #E3EFFF;
      --text_color_L2: #92A8E3;
      --text_color_L3: #6F80A4;
      --text_color_L4: #05012B;
      --bg_color_L1: #05012B;
      --bg_color_L2: #011341;
      --bg_color_L3: #001C54;
      --Dividing-line_color: #022C68;
      --sheet_nva_color: #2C5ECA;
      --sheet_detail_bg_color: #000C33;
      --icon1: rgba(12, 195, 159, .6);
      --icon2: rgba(12, 195, 159, .25);
      --tab1: #21D9CC;
      --tab2: #BED921;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    body {
      background: var(--bg_HomeModule_Padding);
      min-height: 100vh;
      color: var(--text_color_L1);
      padding: 0;
      padding-bottom: 70px;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 12px 15px;
      background: var(--bg_color_L2);
      box-shadow: 0 8px 25px rgba(0, 28, 84, 0.4);
      border-bottom: 2px solid var(--Dividing-line_color);
    }
    
    .logo-section {
      display: flex;
      align-items: center;
    }
    
    .user-header-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
    }
    
    .user-header-name {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--main-color);
    }
    
    .user-header-balance {
      font-size: 0.8rem;
      color: #fbbf24;
      font-weight: 500;
    }
    
    .chicken-icon {
      width: 35px;
      height: 35px;
      background: var(--main_gradient-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      margin-right: 12px;
      box-shadow: 0 4px 15px rgba(0, 236, 190, 0.4);
    }
    
    .app-title {
      font-size: 1.2rem;
      font-weight: bold;
      color: var(--text_color_L1);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .nav-buttons {
      display: flex;
      gap: 12px;
    }
    
    .nav-btn {
      padding: 10px 18px;
      border-radius: 8px;
      font-size: 0.85rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
    }
    
    .nav-btn.login {
      background: rgba(255, 255, 255, 0.15);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
    }
    
    .nav-btn.login:hover {
      background: rgba(255, 255, 255, 0.25);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
    }
    
    .nav-btn.register {
      background: var(--main_gradient-color);
      color: var(--text_color_L4);
      border: none;
      box-shadow: 0 4px 15px rgba(0, 236, 190, 0.4);
    }
    
    .nav-btn.register:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 236, 190, 0.6);
    }
    
    /* Header Action Buttons */
    .header-actions {
      display: flex;
      gap: 8px;
    }
    
    .header-action-btn {
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 0.75rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .header-action-btn.deposit {
      background: rgba(23, 177, 94, 0.2);
      color: var(--Secondary_green_color);
      border: 1px solid rgba(23, 177, 94, 0.3);
    }
    
    .header-action-btn.deposit:hover {
      background: rgba(23, 177, 94, 0.3);
      border-color: var(--Secondary_green_color);
      transform: translateY(-1px);
    }
    
    .header-action-btn.withdraw {
      background: rgba(221, 145, 56, 0.2);
      color: var(--norm_secondary-color);
      border: 1px solid rgba(221, 145, 56, 0.3);
    }
    
    .header-action-btn.withdraw:hover {
      background: rgba(221, 145, 56, 0.3);
      border-color: #f59e0b;
      transform: translateY(-1px);
    }
    
    .header-action-icon {
      font-size: 0.8rem;
    }
    
    .main-content {
      max-width: 380px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    .promo-banner {
      background: linear-gradient(135deg, #ff0000 0%, #ff0000 100%);
      border-radius: 16px;
      padding: 18px 20px;
      margin-bottom: 18px;
      position: relative;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(210, 56, 56, 0.4);
      display: flex;
      align-items: center;
      min-height: 130px;
      border: 2px solid var(--norm_red-color);
    }
    
    .promo-banner::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text x="20" y="30" font-size="8" fill="rgba(255,255,255,0.1)">5</text><text x="80" y="60" font-size="6" fill="rgba(255,255,255,0.1)">8</text><text x="40" y="80" font-size="7" fill="rgba(255,255,255,0.1)">3</text></svg>');
      opacity: 0.3;
    }
    
    .promo-content {
      flex: 1;
      text-align: left;
      position: relative;
      z-index: 2;
    }
    
    .promo-character {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      width: 100px;
      height: 100px;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .character-avatar {
      width: 70px;
      height: 70px;
      background: linear-gradient(135deg, #fbbf24, #ffa200);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 35px;
      position: relative;
      box-shadow: 0 5px 20px rgb(255, 183, 0);
    }
    
    .character-hat {
      position: absolute;
      top: -15px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 25px;
      color: var(--norm_green-color);
    }
    
    .floating-coins {
      position: absolute;
      font-size: 18px;
      animation: float 3s ease-in-out infinite;
    }
    
    .coin-1 { top: 10px; right: 35px; animation-delay: 0s; }
    .coin-2 { top: 25px; right: 20px; animation-delay: 0.5s; }
    .coin-3 { top: 40px; right: 30px; animation-delay: 1s; }
    .coin-4 { top: 55px; right: 15px; animation-delay: 1.5s; }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-12px); }
    }
    
    .money-bag {
      position: absolute;
      bottom: 10px;
      right: 20px;
      font-size: 20px;
      color: #fbbf24;
      animation: bounce 2s ease-in-out infinite;
    }
    
    @keyframes bounce {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.15); }
    }
    
    .matryoshka-dolls {
      position: absolute;
      right: 5px;
      top: 18px;
      display: flex;
      flex-direction: column;
      gap: 2px;
      z-index: 1;
    }
    
    .doll {
      font-size: 12px;
      color: #fbbf24;
      opacity: 0.8;
    }
    
    .character-dress {
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 25px;
      background: linear-gradient(135deg, #10b981, #059669);
      border-radius: 25px 25px 0 0;
      z-index: -1;
    }
    
    .character-bow {
      position: absolute;
      bottom: 5px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 18px;
      color: #ef4444;
    }
    
    .promo-title {
      font-size: 1.2rem;
      font-weight: bold;
      color: #FFD700;
      margin-bottom: 4px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 1;
    }
    
    .promo-amount {
      font-size: 1.4rem;
      color: #FFD700;
      font-weight: bold;
      margin-bottom: 4px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 1;
    }
    
    .promo-subtitle {
      font-size: 0.7rem;
      color: #FFD700;
      font-weight: 600;
      position: relative;
      z-index: 1;
    }
    
    .promo-details {
      background: rgba(255, 255, 255, 0.15);
      border-radius: 8px;
      padding: 8px;
      margin-top: 8px;
      font-size: 0.6rem;
      color: #FFD700;
      text-align: left;
      position: relative;
      z-index: 1;
      backdrop-filter: blur(10px);
    }
    
    .mini-logo {
      position: absolute;
      bottom: 10px;
      right: 15px;
      width: 30px;
      height: 30px;
      background: var(--main_gradient-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      z-index: 1;
    }
    
    .info-strip {
      background: var(--bg_color_L2);
      border-radius: 10px;
      padding: 12px 15px;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      border: 1px solid var(--Dividing-line_color);
      backdrop-filter: blur(10px);
    }
    
    .speaker-icon {
      color: var(--norm_green-color);
      font-size: 1.2rem;
    }
    
    .info-text {
      font-size: 0.8rem;
      color: var(--text_color_L2);
      line-height: 1.4;
    }
    
    .form-container {
      background: var(--bg_color_L2);
      border-radius: 16px;
      padding: 20px;
      border: 2px solid var(--Dividing-line_color);
      box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(10px);
    }
    
    .form-title {
      font-size: 1.1rem;
      font-weight: 600;
      text-align: center;
      color: var(--text_color_L1);
      margin-bottom: 12px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .form-group {
      margin-bottom: 12px;
    }
    
    .input-field {
      width: 100%;
      padding: 12px 15px;
      border-radius: 10px;
      background: var(--bg_color_L3);
      border: 2px solid var(--Dividing-line_color);
      color: var(--text_color_L1);
      font-size: 0.95rem;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }
    
    .input-field::placeholder {
      color: var(--text_color_L3);
    }
    
    .input-field:focus {
      outline: none;
      border-color: var(--main-color);
      box-shadow: 0 0 0 4px rgba(0, 236, 190, 0.3);
      background: var(--bg_color_L2);
    }
    
    .btn {
      width: 100%;
      padding: 18px;
      border-radius: 10px;
      font-weight: 600;
      font-size: 0.9rem;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-top: 5px;
    }
    
    .btn-primary {
      background: var(--main_gradient-color);
      color: var(--text_color_L4);
      box-shadow: 0 6px 20px rgba(0, 236, 190, 0.4);
    }
    
    .btn-primary:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 236, 190, 0.6);
    }
    
    .btn-success {
      background: var(--main_gradient-color);
      color: var(--text_color_L4);
      box-shadow: 0 6px 20px rgba(0, 236, 190, 0.4);
    }
    
    .btn-success:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 236, 190, 0.6);
    }
    
    .switch-text {
      text-align: center;
      font-size: 0.95rem;
      color: var(--text_color_L2);
      margin-top: 25px;
    }
    
    .switch-link {
      color: var(--main-color);
      text-decoration: none;
      font-weight: 600;
      transition: color 0.3s ease;
    }
    
    .switch-link:hover {
      color: var(--main-gradient-start);
    }
    
    .message {
      margin-top: 18px;
      padding: 15px;
      border-radius: 10px;
      font-size: 0.9rem;
      text-align: center;
    }
    
    .message.success {
      background: rgba(23, 177, 94, 0.2);
      border: 2px solid rgba(23, 177, 94, 0.3);
      color: var(--Secondary_green_color);
    }
    
    .message.error {
      background: rgba(210, 56, 56, 0.2);
      border: 2px solid rgba(210, 56, 56, 0.3);
      color: var(--Secondary_red_color);
    }
    
    .hidden {
      display: none;
    }
    
    .loading {
      opacity: 0.7;
      pointer-events: none;
    }
    
    /* Bottom Navigation Bar */
    .bottom-nav {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: var(--bg_color_L2);
      backdrop-filter: blur(15px);
      border-top: 2px solid var(--Dividing-line_color);
      padding: 10px 0;
      display: flex;
      justify-content: space-around;
      align-items: center;
      z-index: 1000;
    }
    
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      color: var(--text_color_L2);
      font-size: 0.8rem;
      font-weight: 500;
      transition: all 0.3s ease;
      padding: 8px 12px;
      border-radius: 10px;
      min-width: 60px;
    }
    
    .nav-item:hover {
      color: var(--text_color_L1);
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
    }
    
    .nav-item.active {
      color: var(--main-color);
      background: rgba(0, 236, 190, 0.2);
    }
    
    .nav-icon {
      font-size: 1.2rem;
      margin-bottom: 4px;
    }
    
    .nav-text {
      font-size: 0.7rem;
      text-align: center;
    }
    
    /* Play Now Button Styles */
    .play-now-container {
      text-align: center;
      padding: 30px 0;
    }
    
    .play-now-btn {
      background: var(--main_gradient-color);
      color: var(--text_color_L4);
      border: none;
      padding: 20px 50px;
      border-radius: 30px;
      font-size: 1.4rem;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 10px 30px rgba(0, 236, 190, 0.5);
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    
    .play-now-btn:hover {
      transform: translateY(-4px);
      box-shadow: 0 15px 40px rgba(0, 236, 190, 0.7);
    }
    
    /* Recharge Buttons Styles */
    .recharge-section {
      margin-bottom: 25px;
    }
    
    .recharge-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text_color_L1);
      text-align: center;
      margin-bottom: 15px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .recharge-buttons {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 20px;
    }
    
    .recharge-btn {
      background: var(--bg_color_L3);
      border: 2px solid var(--Dividing-line_color);
      border-radius: 12px;
      padding: 15px 12px;
      color: var(--text_color_L1);
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }
    
    .recharge-btn:hover {
      background: var(--bg_color_L2);
      border-color: var(--main-color);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 236, 190, 0.3);
    }
    
    .recharge-btn.deposit {
      border-color: var(--norm_green-color);
    }
    
    .recharge-btn.deposit:hover {
      border-color: var(--Secondary_green_color);
      box-shadow: 0 6px 20px rgba(23, 177, 94, 0.3);
    }
    
    .recharge-btn.withdraw {
      border-color: var(--norm_secondary-color);
    }
    
    .recharge-btn.withdraw:hover {
      border-color: #f59e0b;
      box-shadow: 0 6px 20px rgba(221, 145, 56, 0.3);
    }
    
    .recharge-icon {
      font-size: 1.5rem;
    }
    
    .recharge-text {
      font-size: 0.85rem;
      font-weight: 600;
    }
    
    .recharge-amount {
      font-size: 0.8rem;
      color: var(--text_color_L2);
      font-weight: 500;
    }
    
    .welcome-text {
      font-size: 1.3rem;
      color: var(--text_color_L2);
      margin-bottom: 20px;
      text-align: center;
    }
    
    .user-info {
      background: var(--bg_color_L2);
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 25px;
      border: 2px solid var(--Dividing-line_color);
      text-align: center;
      backdrop-filter: blur(10px);
    }
    
    .user-avatar {
      width: 70px;
      height: 70px;
      background: var(--main_gradient-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      margin: 0 auto 15px;
      box-shadow: 0 6px 20px rgba(0, 236, 190, 0.4);
    }
    
    .user-name {
      font-size: 1.3rem;
      font-weight: bold;
      color: var(--text_color_L1);
      margin-bottom: 8px;
    }
    
    .user-balance {
      font-size: 1.1rem;
      color: var(--main-color);
      font-weight: 600;
    }
    
    @media (max-width: 480px) {
      body {
        padding: 0;
        padding-bottom: 80px;
      }
      
      .header {
        margin-bottom: 20px;
        padding: 15px 18px;
      }
      
      .app-title {
        font-size: 1.1rem;
      }
      
      .nav-btn {
        padding: 8px 15px;
        font-size: 0.8rem;
      }
      
      .header-actions {
        gap: 6px;
      }
      
      .header-action-btn {
        padding: 6px 8px;
        font-size: 0.7rem;
      }
      
      .header-action-icon {
        font-size: 0.7rem;
      }
      
      .main-content {
        padding: 0 15px;
      }
      
      .promo-title {
        font-size: 1.4rem;
      }
      
      .promo-amount {
        font-size: 1.6rem;
      }
      
      .form-container {
        padding: 20px;
      }
      
      .input-field {
        padding: 14px 16px;
        font-size: 0.95rem;
      }
      
      .btn {
        padding: 15px;
        font-size: 0.95rem;
      }
      
      .bottom-nav {
        padding: 8px 0;
      }
      
      .nav-item {
        padding: 6px 8px;
        min-width: 55px;
      }
      
      .nav-icon {
        font-size: 1.1rem;
      }
      
      .nav-text {
        font-size: 0.65rem;
      }
      
      .play-now-btn {
        padding: 18px 40px;
        font-size: 1.3rem;
      }
      
      .recharge-buttons {
        grid-template-columns: 1fr;
        gap: 10px;
      }
      
      .recharge-btn {
        padding: 12px 10px;
        font-size: 0.85rem;
      }
      
      .recharge-icon {
        font-size: 1.3rem;
      }
    }
  </style>
</head>
<body>
  <!-- Header with Logo and Navigation -->
  <div class="header">
    <div class="logo-section">
      <div class="chicken-icon">🐔</div>
      <div class="app-title">CHICKEN ROAD</div>
    </div>
    <div class="nav-buttons">
      <button onclick="showLogin()" class="nav-btn login">Log in</button>
      <button onclick="showRegister()" class="nav-btn register">Register</button>
    </div>
    <div class="user-header-info" id="userHeaderInfo" style="display: none;">
      <div class="user-header-name" id="userHeaderName">Welcome!</div>
      <div class="user-header-balance" id="userHeaderBalance">Balance: ₹0</div>
      <div class="header-actions">
        <a href="deposit.html" class="header-action-btn deposit">
          <span class="header-action-icon">💰</span>
          <span>Deposit</span>
        </a>
        <a href="withdraw.html" class="header-action-btn withdraw">
          <span class="header-action-icon">💸</span>
          <span>Withdraw</span>
        </a>
      </div>
    </div>
  </div>

  <div class="main-content">
    <!-- Promotional Banner -->
    <div class="promo-banner">
      <div class="promo-content">
        <div class="promo-title">REFER & EARN</div>
        <div class="promo-amount">GET ₹28</div>
        <div class="promo-subtitle">EVERY INVITATION YOU DID</div>
        <div class="promo-details">
          REWARD WILL AUTOMATICALLY PROCESS TO YOUR ACCOUNT RIGHT AFTER YOUR DOWNLINE MADE SUCCESSFUL FIRST RECHARGE.
        </div>
      </div>
      
      <div class="promo-character">
        <div class="matryoshka-dolls">
          <div class="doll">🎎</div>
          <div class="doll">🎎</div>
          <div class="doll">🎎</div>
        </div>
        <div class="character-avatar">
          👩
          <div class="character-hat">🎩</div>
          <div class="character-dress"></div>
          <div class="character-bow">🎀</div>
        </div>
        <div class="floating-coins coin-1">🪙</div>
        <div class="floating-coins coin-2">🪙</div>
        <div class="floating-coins coin-3">🪙</div>
        <div class="floating-coins coin-4">🪙</div>
        <div class="money-bag">💰</div>
      </div>
      
      <div class="mini-logo">🐔</div>
    </div>
    
    <!-- Information Strip -->
    <div class="info-strip">
      <div class="speaker-icon">🔊</div>
      <div class="info-text">The games are independently developed by our team, fun, fair, and safe.</div>
    </div>
    
    <!-- Login Form -->
    <div id="loginForm">
      <div class="form-container">
        <h2 class="form-title">Welcome Back</h2>
        
        <div class="form-group">
          <input type="text" id="loginUsername" placeholder="Username or Phone Number" class="input-field" />
        </div>
        
        <div class="form-group">
          <input type="password" id="loginPassword" placeholder="Password" class="input-field" />
        </div>
        
        <button onclick="login()" class="btn btn-primary" id="loginBtn">Login</button>
      </div>
    </div>
    
    <!-- Register Form -->
    <div id="registerForm" class="hidden">
      <div class="form-container">
        <h2 class="form-title">Create Account</h2>
        
        <div class="form-group">
          <input type="text" id="regUsername" placeholder="Username" class="input-field" />
        </div>
        
        <div class="form-group">
          <input type="tel" id="regPhone" placeholder="Phone Number" class="input-field" />
        </div>
        
        <div class="form-group">
          <input type="password" id="regPassword" placeholder="Password" class="input-field" />
        </div>
        
        <div class="form-group">
          <input type="text" id="referralCode" placeholder="Referral Code (Optional)" class="input-field" />
        </div>
        
        <button onclick="register()" class="btn btn-success" id="registerBtn">Register</button>
      </div>
    </div>
    
    <!-- Error/Success Messages -->
    <div id="message" class="message hidden"></div>
    
    <!-- Logged In User View -->
    <div id="loggedInView" class="hidden">
      <!-- Recharge Section -->
      <div class="recharge-section">
        
      </div>
      
      <div class="play-now-container">
        <div class="welcome-text">Ready to play and earn?</div>
        <button onclick="playNow()" class="play-now-btn">🎮 PLAY NOW</button>
      </div>
    </div>
  </div>

  <script>
    function showRegister() {
      document.getElementById('loginForm').classList.add('hidden');
      document.getElementById('registerForm').classList.remove('hidden');
      hideMessage();
      
      // Check for referral code in URL when switching to register form
      fillReferralCodeFromURL();
      
      // Update navigation buttons
      document.querySelector('.nav-btn.login').classList.remove('register');
      document.querySelector('.nav-btn.register').classList.add('login');
      document.querySelector('.nav-btn.login').textContent = 'Log in';
      document.querySelector('.nav-btn.register').textContent = 'Register';
    }
    
    function showLogin() {
      document.getElementById('registerForm').classList.add('hidden');
      document.getElementById('loginForm').classList.remove('hidden');
      hideMessage();
      
      // Update navigation buttons
      document.querySelector('.nav-btn.login').classList.remove('register');
      document.querySelector('.nav-btn.register').classList.add('login');
      document.querySelector('.nav-btn.login').textContent = 'Log in';
      document.querySelector('.nav-btn.register').textContent = 'Register';
    }
    
    function showMessage(text, isError = false) {
      const messageDiv = document.getElementById('message');
      messageDiv.textContent = text;
      messageDiv.className = `message ${isError ? 'error' : 'success'}`;
      messageDiv.classList.remove('hidden');
    }
    
    function hideMessage() {
      document.getElementById('message').classList.add('hidden');
    }
    
    function setLoading(buttonId, isLoading) {
      const button = document.getElementById(buttonId);
      if (isLoading) {
        button.classList.add('loading');
        button.textContent = 'Loading...';
      } else {
        button.classList.remove('loading');
        button.textContent = buttonId === 'loginBtn' ? 'Login' : 'Register';
      }
    }
    
    async function login() {
      const username = document.getElementById('loginUsername').value.trim();
      const password = document.getElementById('loginPassword').value;
      
      if (!username || !password) {
        showMessage('Please fill in all fields', true);
        return;
      }
      
      setLoading('loginBtn', true);
      
      try {
        const response = await fetch('api/login.php', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        if (data.success) {
          showMessage('Login successful!');
          setTimeout(() => {
            showLoggedInView(data.user);
            hideMessage();
          }, 1000);
        } else {
          showMessage(data.error || 'Login failed', true);
        }
      } catch (error) {
        showMessage('Network error. Please try again.', true);
      } finally {
        setLoading('loginBtn', false);
      }
    }
    
    async function register() {
      const username = document.getElementById('regUsername').value.trim();
      const phone = document.getElementById('regPhone').value.trim();
      const password = document.getElementById('regPassword').value;
      const referralCode = document.getElementById('referralCode').value.trim();
      
      if (!username || !phone || !password) {
        showMessage('Please fill in all required fields', true);
        return;
      }
      
      if (password.length < 6) {
        showMessage('Password must be at least 6 characters', true);
        return;
      }
      
      setLoading('registerBtn', true);
      
      try {
        const response = await fetch('api/register.php', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username, phone, password, referral_code: referralCode })
        });
        
        const data = await response.json();
        
        if (data.success) {
          showMessage('Registration successful!');
          setTimeout(() => {
            showLoggedInView(data.user);
            hideMessage();
          }, 1000);
        } else {
          showMessage(data.error || 'Registration failed', true);
        }
      } catch (error) {
        showMessage('Network error. Please try again.', true);
      } finally {
        setLoading('registerBtn', false);
      }
    }
    
    // Function to extract referral code from URL and fill the field
    function fillReferralCodeFromURL() {
      const urlParams = new URLSearchParams(window.location.search);
      const referralCode = urlParams.get('ref');
      
      if (referralCode) {
        const referralInput = document.getElementById('referralCode');
        if (referralInput) {
          referralInput.value = referralCode;
          // Show a small notification that referral code was auto-filled
          showMessage(`Referral code "${referralCode}" has been auto-filled!`, false);
          setTimeout(() => hideMessage(), 3000);
        }
        
        // If there's a referral code, automatically show register form
        if (!document.getElementById('registerForm').classList.contains('hidden')) {
          // Already on register form, do nothing
        } else if (document.getElementById('loginForm').classList.contains('hidden')) {
          // User is logged in, do nothing
        } else {
          // User is on login form, switch to register form
          showRegister();
        }
      }
    }
    
    // Check if user is already logged in
    window.addEventListener('DOMContentLoaded', async () => {
      // First, check for referral code in URL and fill it
      fillReferralCodeFromURL();
      
      try {
        const response = await fetch('api/user.php');
        const data = await response.json();
        
        if (data.success) {
          // User is logged in, show logged in view
          showLoggedInView(data.user);
        }
      } catch (error) {
        // User not logged in, stay on login page
      }
    });
    
    // Function to show logged in view
    function showLoggedInView(user) {
      // Hide login/register forms
      document.getElementById('loginForm').classList.add('hidden');
      document.getElementById('registerForm').classList.add('hidden');
      
      // Hide header navigation buttons
      document.querySelector('.nav-buttons').style.display = 'none';
      
      // Show logged in view
      document.getElementById('loggedInView').classList.remove('hidden');
      
      // Show and update user info in header
      if (user) {
        document.getElementById('userHeaderInfo').style.display = 'flex';
        document.getElementById('userHeaderName').textContent = `Welcome, ${user.username}!`;
        document.getElementById('userHeaderBalance').textContent = `Balance: ₹${user.balance || 0}`;
      }
    }
    
    // Function to handle Play Now button
    function playNow() {
      window.location.href = 'game.html';
    }
    
    // Add enter key support
    document.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        if (!document.getElementById('loginForm').classList.contains('hidden')) {
          login();
        } else {
          register();
        }
      }
    });
    
    // Function to stay on current page (for Home button)
    function stayOnCurrentPage() {
      // Do nothing - user stays on current page
      // This is useful for the Home button since we're already on the home/login page
    }
    
    // Function to check authentication before navigation
    async function checkAuthAndNavigate(targetPage) {
      try {
        const response = await fetch('api/user.php');
        const data = await response.json();
        
        if (data.success) {
          // User is logged in, navigate to target page
          window.location.href = targetPage;
        } else {
          // User is not logged in, show message and stay on login page
          showMessage('Please login first to access this page', true);
        }
      } catch (error) {
        // Network error, show message
        showMessage('Please login first to access this page', true);
      }
    }
  </script>
  
  <!-- Bottom Navigation Bar -->
  <div class="bottom-nav">
    <a href="#" onclick="stayOnCurrentPage()" class="nav-item active">
      <div class="nav-icon">🏠</div>
      <div class="nav-text">Home</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('refer.html')" class="nav-item">
      <div class="nav-icon">👥</div>
      <div class="nav-text">Promotion</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('game.html')" class="nav-item">
      <div class="nav-icon">🎮</div>
      <div class="nav-text">Games</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('bet-history.html')" class="nav-item">
      <div class="nav-icon">💰</div>
      <div class="nav-text">My Bet</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('transactions.html')" class="nav-item">
      <div class="nav-icon">👤</div>
      <div class="nav-text">My</div>
    </a>
  </div>
</body>
</html> 