<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Deposit - Chicken Road Game</title>
  <style>
    :root {
      --main-color: #00ECBE;
      --main-gradient-start: #7AFEC3;
      --main-gradient-end: #02AFB6;
      --main_gradient-color: linear-gradient(180deg, var(--main-gradient-start), var(--main-gradient-end));
      --main_gradient-color2: linear-gradient(90deg, var(--main-gradient-start), var(--main-gradient-end));
      --bg_HomeModule_Stroke: #224BA2;
      --bg_HomeModule_Padding: linear-gradient(180deg, #001C54 0%, #000C33 100%);
      --norm_red-color: #D23838;
      --norm_green-color: #17B15E;
      --norm_secondary-color: #DD9138;
      --norm_Purple-color: #9B48DB;
      --norm_bule-color: #5088D3;
      --button_dis_color: #3D4863;
      --Secondary_red_color: #FD565C;
      --Secondary_green_color: #40AD72;
      --Secondary_Color1: #FFEBEC;
      --Secondary_Color2: #DAFFEB;
      --Secondary_moto_Color9: #239DA1;
      --Secondary_moto_Color8: #163B86;
      --text_color_L1: #E3EFFF;
      --text_color_L2: #92A8E3;
      --text_color_L3: #6F80A4;
      --text_color_L4: #05012B;
      --bg_color_L1: #05012B;
      --bg_color_L2: #011341;
      --bg_color_L3: #001C54;
      --Dividing-line_color: #022C68;
      --sheet_nva_color: #2C5ECA;
      --sheet_detail_bg_color: #000C33;
      --icon1: rgba(12, 195, 159, .6);
      --icon2: rgba(12, 195, 159, .25);
      --tab1: #21D9CC;
      --tab2: #BED921;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    body {
      background: var(--bg_HomeModule_Padding);
      min-height: 100vh;
      color: var(--text_color_L1);
      padding: 0;
      padding-bottom: 70px;
      position: relative;
      overflow-x: hidden;
    }
    
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 20% 80%, rgba(0, 236, 190, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(155, 72, 219, 0.1) 0%, transparent 50%);
      pointer-events: none;
      z-index: -1;
      animation: backgroundShift 20s ease-in-out infinite;
    }
    
    @keyframes backgroundShift {
      0%, 100% { opacity: 0.5; }
      50% { opacity: 1; }
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 12px 15px;
      background: var(--bg_color_L2);
      box-shadow: 0 8px 25px rgba(0, 28, 84, 0.4);
      border-bottom: 2px solid var(--Dividing-line_color);
    }
    
    .logo-section {
      display: flex;
      align-items: center;
    }
    
    .user-header-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
    }
    
    .user-header-name {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--main-color);
    }
    
    .user-header-balance {
      font-size: 0.8rem;
      color: #fbbf24;
      font-weight: 500;
    }
    
    .chicken-icon {
      width: 35px;
      height: 35px;
      background: var(--main_gradient-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      margin-right: 12px;
      box-shadow: 0 4px 15px rgba(0, 236, 190, 0.4);
    }
    
    .app-title {
      font-size: 1.2rem;
      font-weight: bold;
      color: var(--text_color_L1);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .nav-buttons {
      display: flex;
      gap: 12px;
    }
    
    .nav-btn {
      padding: 10px 18px;
      border-radius: 8px;
      font-size: 0.85rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
    }
    
    .nav-btn.back {
      background: rgba(255, 255, 255, 0.15);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
    }
    
    .nav-btn.back:hover {
      background: rgba(255, 255, 255, 0.25);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
    }
    
    .main-content {
      max-width: 380px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    .deposit-card {
      background: var(--bg_color_L2);
      border-radius: 16px;
      border: 2px solid var(--Dividing-line_color);
      box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
      padding: 20px;
      margin-bottom: 20px;
      backdrop-filter: blur(10px);
    }
    
    .card-title {
      color: var(--text_color_L1);
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .form-group {
      margin-bottom: 16px;
    }
    
    .form-label {
      color: var(--text_color_L1);
      font-size: 1rem;
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
    }
    
    .form-input {
      width: 100%;
      padding: 12px 15px;
      border-radius: 10px;
      background: var(--bg_color_L3);
      border: 2px solid var(--Dividing-line_color);
      color: var(--text_color_L1);
      font-size: 0.95rem;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      box-sizing: border-box;
    }
    
    .form-input:focus {
      outline: none;
      border-color: var(--main-color);
      box-shadow: 0 0 0 4px rgba(0, 236, 190, 0.3);
      background: var(--bg_color_L2);
      transform: translateY(-1px);
    }
    
    .form-input::placeholder {
      color: var(--text_color_L3);
    }
    
    .form-select {
      width: 100%;
      padding: 12px 15px;
      background: var(--bg_color_L3);
      border: 2px solid var(--Dividing-line_color);
      border-radius: 10px;
      color: var(--text_color_L1);
      font-size: 0.95rem;
      box-sizing: border-box;
      cursor: pointer;
    }
    
    .form-select:focus {
      outline: none;
      border-color: var(--main-color);
      background: var(--bg_color_L2);
    }
    
    .min-max-info {
      color: var(--text_color_L3);
      font-size: 0.85rem;
      margin-top: 4px;
    }
    
    .payment-details {
      background: var(--bg_color_L3);
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 20px;
      border: 1px solid var(--Dividing-line_color);
    }
    
    .payment-details h4 {
      color: var(--text_color_L1);
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    .payment-details p {
      color: var(--text_color_L2);
      font-size: 0.9rem;
      margin-bottom: 12px;
      line-height: 1.4;
    }
    
    .qr-code-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }
    
    .qr-code-image {
      width: 128px;
      height: 128px;
      border-radius: 8px;
      background: white;
      padding: 8px;
    }
    
    .qr-placeholder {
      width: 128px;
      height: 128px;
      background: var(--bg_color_L2);
      border: 2px dashed var(--Dividing-line_color);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text_color_L3);
      font-size: 0.8rem;
    }
    
    .upi-id {
      color: var(--text_color_L2);
      font-size: 0.9rem;
    }
    
    .upi-id-value {
      color: var(--main-color);
      font-weight: 600;
    }
    
    .bank-details {
      color: var(--text_color_L2);
      font-size: 0.9rem;
      white-space: pre-line;
      line-height: 1.5;
    }
    
    .file-input {
      width: 100%;
      padding: 12px 15px;
      border-radius: 10px;
      background: var(--bg_color_L3);
      border: 2px solid var(--Dividing-line_color);
      color: var(--text_color_L1);
      font-size: 0.95rem;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      box-sizing: border-box;
      cursor: pointer;
    }
    
    .file-input:focus {
      outline: none;
      border-color: var(--main-color);
      box-shadow: 0 0 0 4px rgba(0, 236, 190, 0.3);
      background: var(--bg_color_L2);
      transform: translateY(-1px);
    }
    
    .file-input::file-selector-button {
      background: var(--main_gradient-color);
      color: var(--text_color_L4);
      border: none;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 0.85rem;
      font-weight: 600;
      cursor: pointer;
      margin-right: 12px;
      transition: all 0.3s ease;
    }
    
    .file-input::file-selector-button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 236, 190, 0.3);
    }
    
    .deposit-btn {
      width: 100%;
      background: var(--main_gradient-color);
      color: var(--text_color_L4);
      border: none;
      border-radius: 10px;
      height: 54px;
      font-size: 1.2rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 6px 20px rgba(0, 236, 190, 0.4);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .deposit-btn:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow: 0 8px 25px rgba(0, 236, 190, 0.6);
    }
    
    .deposit-btn:active {
      transform: translateY(-1px) scale(0.98);
    }
    
    .deposit-btn:disabled {
      background: var(--button_dis_color);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    /* Modal Styles */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.7);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    
    .modal-content {
      background: var(--bg_color_L2);
      border-radius: 18px;
      padding: 24px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border: 2px solid var(--Dividing-line_color);
      text-align: center;
      max-width: 320px;
      width: 90%;
      position: relative;
    }
    
    .modal-close {
      position: absolute;
      top: 12px;
      right: 12px;
      background: rgba(64,75,79,0.12);
      border: none;
      font-size: 2.1rem;
      color: #a1a1aa;
      cursor: pointer;
      border-radius: 50%;
      width: 38px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.2s, color 0.2s;
    }
    
    .modal-close:hover {
      color: #f87171;
      background: rgba(64,75,79,0.22);
    }
    
    .modal-title {
      font-size: 1.5rem;
      font-weight: bold;
      color: var(--text_color_L1);
      margin-bottom: 16px;
    }
    
    .modal-message {
      color: var(--text_color_L2);
      font-size: 1rem;
      margin-bottom: 20px;
      line-height: 1.5;
    }
    
    .modal-btn {
      background: var(--main_gradient-color);
      color: var(--text_color_L4);
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .modal-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 236, 190, 0.4);
    }
    
    /* Auth Modal */
    .auth-form {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 20px;
    }
    
    .auth-toggle {
      color: var(--main-color);
      cursor: pointer;
      text-decoration: underline;
      font-size: 0.9rem;
    }
    
    .auth-toggle:hover {
      color: var(--main-gradient-start);
    }
    
    .error-message {
      color: var(--Secondary_red_color);
      font-size: 0.9rem;
      margin-top: 8px;
    }
    
    /* Bottom Navigation Bar */
    .bottom-nav {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: var(--bg_color_L2);
      backdrop-filter: blur(15px);
      border-top: 2px solid var(--Dividing-line_color);
      padding: 10px 0;
      display: flex;
      justify-content: space-around;
      align-items: center;
      z-index: 1000;
    }
    
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      color: var(--text_color_L2);
      font-size: 0.8rem;
      font-weight: 500;
      transition: all 0.3s ease;
      padding: 8px 12px;
      border-radius: 10px;
      min-width: 60px;
    }
    
    .nav-item:hover {
      color: var(--text_color_L1);
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 236, 190, 0.3);
    }
    
    .nav-item:active {
      transform: translateY(0px) scale(0.95);
    }
    
    .nav-item.active {
      color: var(--main-color);
      background: rgba(0, 236, 190, 0.2);
    }
    
    .nav-icon {
      font-size: 1.2rem;
      margin-bottom: 4px;
    }
    
    .nav-text {
      font-size: 0.7rem;
      text-align: center;
    }
    
    @media (max-width: 480px) {
      body {
        padding: 0;
        padding-bottom: 80px;
      }
      
      .header {
        margin-bottom: 20px;
        padding: 15px 18px;
      }
      
      .app-title {
        font-size: 1.1rem;
      }
      
      .nav-btn {
        padding: 8px 15px;
        font-size: 0.8rem;
      }
      
      .main-content {
        padding: 0 15px;
      }
      
      .deposit-card {
        padding: 20px;
      }
      
      .form-input {
        padding: 14px 16px;
        font-size: 0.95rem;
      }
      
      .deposit-btn {
        padding: 15px;
        font-size: 0.95rem;
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <div class="header">
    <div class="logo-section">
      <div class="chicken-icon">🐔</div>
      <div class="app-title">DEPOSIT FUNDS</div>
    </div>
    <div class="user-header-info" id="userHeaderInfo" style="display: none;">
      <div class="user-header-name" id="userHeaderName">Welcome!</div>
      <div class="user-header-balance" id="userHeaderBalance">Balance: ₹0</div>
    </div>
    <div class="nav-buttons">
      <button class="nav-btn back" onclick="window.location.href='index.html'">Back</button>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <div class="deposit-card">
      <div class="card-title">Deposit Request</div>
      
      <!-- Amount Input -->
      <div class="form-group">
        <label class="form-label">Amount (₹)</label>
        <input type="number" id="depositAmount" class="form-input" placeholder="Enter amount" min="1">
        <div class="min-max-info" id="minMaxInfo">Min: ₹10 | Max: ₹10000</div>
      </div>

      <!-- Deposit Method -->
      <div class="form-group">
        <label class="form-label">Deposit Method</label>
        <select id="depositMethod" class="form-select">
          <option value="qr">QR Code</option>
          <option value="bank">Bank Transfer</option>
        </select>
      </div>

      <!-- Payment Details -->
      <div id="paymentDetails"></div>

      <!-- Transaction ID -->
      <div class="form-group">
        <label class="form-label">Transaction ID</label>
        <input type="text" id="transactionId" class="form-input" placeholder="Enter Transaction ID">
      </div>

      <!-- Screenshot Upload -->
      <div class="form-group">
        <label class="form-label">Upload Screenshot</label>
        <input type="file" id="screenshot" class="file-input" accept="image/*">
      </div>

      <!-- Deposit Button -->
      <button id="depositBtn" class="deposit-btn">
        Process Deposit
      </button>
    </div>
  </div>

  <!-- Result Modal -->
  <div id="resultModal" class="modal">
    <div class="modal-content">
      <button class="modal-close" onclick="closeResultModal()">&times;</button>
      <div class="modal-title" id="resultTitle">Deposit Submitted!</div>
      <div class="modal-message" id="resultMessage">Your deposit request has been submitted successfully.</div>
      <button class="modal-btn" onclick="closeResultModal()">Continue</button>
    </div>
  </div>

  <!-- Auth Modal -->
  <div id="authModal" class="modal">
    <div class="modal-content">
      <button class="modal-close" onclick="closeAuthModal()">&times;</button>
      <div class="modal-title" id="authTitle">Login</div>
      <div class="auth-form">
        <input type="text" id="authUsername" class="form-input" placeholder="Username">
        <input type="password" id="authPassword" class="form-input" placeholder="Password">
        <div class="error-message" id="authError" style="display: none;"></div>
      </div>
      <button class="modal-btn" id="authActionBtn">Login</button>
      <div style="margin-top: 12px;">
        <span class="auth-toggle" id="toggleAuthMode">Don't have an account? Register</span>
      </div>
    </div>
  </div>

  <script>
    let selectedMethod = 'qr';
    let minAmount = 10;
    let maxAmount = 10000;
    let settings = {};
    
    // Auth integration
    function showAuthModal() {
      document.getElementById('authModal').style.display = 'flex';
      document.getElementById('authError').style.display = 'none';
    }
    
    function closeAuthModal() {
      document.getElementById('authModal').style.display = 'none';
    }
    
    function fetchUser() {
      return fetch('api/user.php').then(r => r.json());
    }
    
    function requireLogin() {
      fetchUser().then(data => {
        if (!data.success) showAuthModal();
        else loadUserData();
      });
    }
    
    window.addEventListener('DOMContentLoaded', requireLogin);
    
    // Auth modal logic
    let authMode = 'login';
    document.getElementById('toggleAuthMode').onclick = function() {
      authMode = authMode === 'login' ? 'register' : 'login';
      document.getElementById('authTitle').textContent = authMode === 'login' ? 'Login' : 'Register';
      document.getElementById('authActionBtn').textContent = authMode === 'login' ? 'Login' : 'Register';
      document.getElementById('toggleAuthMode').textContent = authMode === 'login' ? 'Don\'t have an account? Register' : 'Already have an account? Login';
    };
    
    document.getElementById('authActionBtn').onclick = function() {
      const username = document.getElementById('authUsername').value.trim();
      const password = document.getElementById('authPassword').value;
      const errorDiv = document.getElementById('authError');
      errorDiv.style.display = 'none';
      
      let url = authMode === 'login' ? 'api/login.php' : 'api/register.php';
      let body = authMode === 'login' ? { username, password } : { username, email: username, password };
      
      fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })
      .then(r => r.json())
      .then(data => {
        if (data.success) {
          closeAuthModal();
          loadUserData();
        } else {
          errorDiv.textContent = data.error || 'Authentication failed.';
          errorDiv.style.display = 'block';
        }
      })
      .catch(() => {
        errorDiv.textContent = 'Network error.';
        errorDiv.style.display = 'block';
      });
    };

    // Load user data and settings
    function loadUserData() {
      // Load user balance
      fetch('api/user.php')
        .then(r => r.json())
        .then(data => {
          if (data.success) {
            // Update header info
            document.getElementById('userHeaderInfo').style.display = 'flex';
            document.getElementById('userHeaderName').textContent = `Welcome, ${data.user.username}!`;
            document.getElementById('userHeaderBalance').textContent = `Balance: ₹${parseFloat(data.user.balance || 0).toFixed(2)}`;
          }
        });
      
      // Load settings
      loadSettings();
    }
    
    // Load settings
    function loadSettings() {
      fetch('api/settings.php')
        .then(r => r.json())
        .then(data => {
          if (data.success) {
            settings = data.settings;
            minAmount = parseFloat(settings.min_deposit) || 10;
            maxAmount = parseFloat(settings.max_deposit) || 10000;
            updateMinMaxInfo();
            updatePaymentDetails();
          }
        })
        .catch(() => {
          console.log('Failed to load settings');
        });
    }
    
    function updateMinMaxInfo() {
      document.getElementById('minMaxInfo').textContent = `Min: ₹${minAmount} | Max: ₹${maxAmount}`;
    }
    
    function updatePaymentDetails() {
      const method = document.getElementById('depositMethod').value;
      const detailsDiv = document.getElementById('paymentDetails');
      
      if (method === 'qr') {
        detailsDiv.innerHTML = `
          <div class="payment-details">
            <h4>Scan QR to Pay</h4>
            <div class="qr-code-container">
              ${settings.qr_code ? `<img src="${settings.qr_code}" alt="QR Code" class="qr-code-image">` : '<div class="qr-placeholder">No QR Code</div>'}
              <div class="upi-id">UPI ID: <span class="upi-id-value">${settings.upi_id || 'Not set'}</span></div>
            </div>
          </div>
        `;
      } else if (method === 'bank') {
        detailsDiv.innerHTML = `
          <div class="payment-details">
            <h4>Bank Details</h4>
            <div class="bank-details">${settings.bank_details || 'Bank details not set'}</div>
          </div>
        `;
      } else {
        detailsDiv.innerHTML = '';
      }
    }
    
    // Update payment details when method changes
    const depositMethodSelect = document.getElementById('depositMethod');
    depositMethodSelect.addEventListener('change', updatePaymentDetails);
    
    // Deposit button
    document.getElementById('depositBtn').addEventListener('click', function() {
      const amount = parseFloat(document.getElementById('depositAmount').value);
      const method = depositMethodSelect.value;
      const transactionId = document.getElementById('transactionId').value.trim();
      const screenshot = document.getElementById('screenshot').files[0];
      
      if (!amount || amount < minAmount || amount > maxAmount) {
        alert(`Please enter a valid amount between ₹${minAmount} and ₹${maxAmount}`);
        return;
      }
      if (!transactionId) {
        alert('Please enter the Transaction ID');
        return;
      }
      if (!screenshot) {
        alert('Please upload a screenshot');
        return;
      }
      
      const formData = new FormData();
      formData.append('amount', amount);
      formData.append('deposit_method', method);
      formData.append('transaction_id', transactionId);
      formData.append('screenshot', screenshot);
      
      fetch('api/deposit.php', {
        method: 'POST',
        body: formData
      })
        .then(r => r.json())
        .then(data => {
          if (data.success) {
            document.getElementById('resultTitle').textContent = 'Deposit Request Submitted!';
            document.getElementById('resultMessage').textContent = `Amount: ₹${amount.toFixed(2)}\nStatus: Pending (Admin Approval Required)`;
            document.getElementById('resultModal').style.display = 'flex';
          } else {
            alert(data.error || 'Failed to submit deposit request.');
          }
        })
        .catch(() => {
          alert('Network error.');
        });
    });
    
    // Modal functions
    function closeResultModal() {
      document.getElementById('resultModal').style.display = 'none';
      window.location.href = 'index.html';
    }
    
    function closeAuthModal() {
      document.getElementById('authModal').style.display = 'none';
    }
    
    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
      }
    });
    
    // Function to check authentication before navigation
    async function checkAuthAndNavigate(targetPage) {
      try {
        const response = await fetch('api/user.php');
        const data = await response.json();
        
        if (data.success) {
          // User is logged in, navigate to target page
          window.location.href = targetPage;
        } else {
          // User is not logged in, show message and stay on current page
          alert('Please login first to access this page');
        }
      } catch (error) {
        // Network error, show message
        alert('Please login first to access this page');
      }
    }
    
    // Initialize
    updatePaymentDetails();
  </script>
  
  <!-- Bottom Navigation Bar -->
  <div class="bottom-nav">
    <a href="index.html" class="nav-item">
      <div class="nav-icon">🏠</div>
      <div class="nav-text">Home</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('refer.html')" class="nav-item">
      <div class="nav-icon">👥</div>
      <div class="nav-text">Promotion</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('game.html')" class="nav-item">
      <div class="nav-icon">🎮</div>
      <div class="nav-text">Games</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('bet-history.html')" class="nav-item">
      <div class="nav-icon">💰</div>
      <div class="nav-text">My Bet</div>
    </a>
    <a href="#" onclick="checkAuthAndNavigate('tranjection.html')" class="nav-item active">
      <div class="nav-icon">👤</div>
      <div class="nav-text">My</div>
    </a>
  </div>
</body>
</html> 